/**
 * AGARTHA RPG - MAIN APPLICATION
 * Entry point and application orchestration
 */

import { CONFIG, validateConfig, getModelConfig, getCharacterClass } from './config.js';
import { LORE, getContextualLore, getCharacterLore } from './lore.js';
import { GameEngine } from './game-engine.js';
import { UIManager } from './ui-manager.js';
import { AIManager } from './ai-manager.js';
import { CharacterManager } from './character-manager.js';
import { SaveManager } from './save-manager.js';

/**
 * Main Application Class
 * Coordinates all game systems and manages application lifecycle
 */
class AgarthaApp {
    constructor() {
        this.gameEngine = null;
        this.uiManager = null;
        this.aiManager = null;
        this.characterManager = null;
        this.saveManager = null;
        
        this.initialized = false;
        this.currentScreen = 'model-setup';
        this.selectedModel = null;
        this.currentPlayer = null;
        
        // Bind methods
        this.handleModelSelection = this.handleModelSelection.bind(this);
        this.handleAIInitialization = this.handleAIInitialization.bind(this);
        this.handleCharacterCreation = this.handleCharacterCreation.bind(this);
        this.handleGameStart = this.handleGameStart.bind(this);
    }

    /**
     * Initialize the application
     */
    async initialize() {
        try {
            console.log('🔮 Initializing Agartha RPG...');
            
            // Validate configuration
            if (!validateConfig()) {
                throw new Error('Configuration validation failed');
            }
            
            // Initialize managers
            this.uiManager = new UIManager();
            this.aiManager = new AIManager();
            this.characterManager = new CharacterManager();
            this.saveManager = new SaveManager();
            this.gameEngine = new GameEngine(this.aiManager, this.characterManager, this.saveManager);
            
            // Initialize UI
            await this.uiManager.initialize();
            
            // Set up event listeners
            this.setupEventListeners();
            
            // Populate model options
            this.populateModelOptions();
            
            // Check for saved game
            await this.checkForSavedGame();
            
            // Show initial screen
            this.showScreen('model-setup');
            
            this.initialized = true;
            console.log('✅ Agartha RPG initialized successfully');
            
        } catch (error) {
            console.error('❌ Failed to initialize Agartha RPG:', error);
            this.showError('Failed to initialize the game. Please refresh and try again.');
        }
    }

    /**
     * Set up event listeners for the application
     */
    setupEventListeners() {
        // Model selection
        document.addEventListener('click', (e) => {
            if (e.target.closest('.model-option')) {
                this.handleModelSelection(e.target.closest('.model-option'));
            }
        });
        
        // AI initialization
        const startBtn = document.getElementById('startBtn');
        if (startBtn) {
            startBtn.addEventListener('click', this.handleAIInitialization);
        }
        
        // Character creation
        const enterGameBtn = document.getElementById('enterGameBtn');
        if (enterGameBtn) {
            enterGameBtn.addEventListener('click', this.handleCharacterCreation);
        }
        
        // Character class selection
        const characterClass = document.getElementById('characterClass');
        if (characterClass) {
            characterClass.addEventListener('change', this.updateClassDescription.bind(this));
        }
        
        // Character form changes
        const playerName = document.getElementById('playerName');
        const characterDesc = document.getElementById('characterDesc');
        
        if (playerName) {
            playerName.addEventListener('input', this.updateCharacterPreview.bind(this));
        }
        
        if (characterDesc) {
            characterDesc.addEventListener('input', this.updateCharacterPreview.bind(this));
        }
        
        // Game controls
        const sendBtn = document.getElementById('sendBtn');
        const messageInput = document.getElementById('messageInput');
        
        if (sendBtn) {
            sendBtn.addEventListener('click', this.handleSendMessage.bind(this));
        }
        
        if (messageInput) {
            messageInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    this.handleSendMessage();
                }
            });
            
            messageInput.addEventListener('input', this.updateCharacterCount.bind(this));
        }
        
        // Quick actions
        document.addEventListener('click', (e) => {
            if (e.target.closest('.quick-action')) {
                const action = e.target.closest('.quick-action').dataset.action;
                this.handleQuickAction(action);
            }
        });
        
        // Modal controls
        const modalClose = document.getElementById('modalClose');
        const modalOverlay = document.getElementById('modalOverlay');
        
        if (modalClose) {
            modalClose.addEventListener('click', () => this.uiManager.hideModal());
        }
        
        if (modalOverlay) {
            modalOverlay.addEventListener('click', (e) => {
                if (e.target === modalOverlay) {
                    this.uiManager.hideModal();
                }
            });
        }
        
        // Keyboard shortcuts
        document.addEventListener('keydown', this.handleKeyboardShortcuts.bind(this));
        
        // Window events
        window.addEventListener('beforeunload', this.handleBeforeUnload.bind(this));
        window.addEventListener('resize', this.handleResize.bind(this));
    }

    /**
     * Populate model selection options
     */
    populateModelOptions() {
        const container = document.getElementById('modelOptions');
        if (!container) return;
        
        container.innerHTML = '';
        
        Object.entries(CONFIG.models).forEach(([modelId, config]) => {
            const option = document.createElement('div');
            option.className = 'model-option hover-lift';
            option.dataset.model = modelId;
            
            option.innerHTML = `
                <h4>${config.displayName}</h4>
                <p>${config.description}</p>
                <div class="model-specs">
                    <span>📦 ${config.size}</span>
                    <span>🧠 ${config.params}</span>
                    <span>⚡ ${config.speed}</span>
                </div>
                ${config.recommended ? '<div class="badge badge-success mt-sm">Recommended</div>' : ''}
            `;
            
            container.appendChild(option);
        });
    }

    /**
     * Populate character class options
     */
    populateCharacterClasses() {
        const select = document.getElementById('characterClass');
        if (!select) return;
        
        select.innerHTML = '<option value="">Choose your sacred path...</option>';
        
        Object.entries(CONFIG.characterClasses).forEach(([className, config]) => {
            const option = document.createElement('option');
            option.value = className;
            option.textContent = `${config.name} - ${config.title}`;
            select.appendChild(option);
        });
    }

    /**
     * Handle model selection
     */
    handleModelSelection(element) {
        // Remove previous selection
        document.querySelectorAll('.model-option').forEach(el => {
            el.classList.remove('selected');
        });
        
        // Select new model
        element.classList.add('selected');
        this.selectedModel = element.dataset.model;
        
        // Update UI
        const statusText = document.getElementById('statusText');
        const startBtn = document.getElementById('startBtn');
        const statusIndicator = document.getElementById('statusIndicator');
        
        if (statusText) {
            const config = getModelConfig(this.selectedModel);
            statusText.textContent = `Selected: ${config.name} - Ready to initialize`;
        }
        
        if (startBtn) {
            startBtn.disabled = false;
        }
        
        if (statusIndicator) {
            statusIndicator.className = 'status-indicator online';
        }
    }

    /**
     * Handle AI initialization
     */
    async handleAIInitialization() {
        if (!this.selectedModel) {
            this.uiManager.showNotification('Please select an AI model first.', 'warning');
            return;
        }

        try {
            const startBtn = document.getElementById('startBtn');
            const statusDiv = document.getElementById('modelStatus');
            
            if (startBtn) startBtn.disabled = true;
            if (statusDiv) statusDiv.classList.add('loading');
            
            // Initialize AI
            const success = await this.aiManager.initialize(this.selectedModel, {
                onProgress: this.updateInitializationProgress.bind(this)
            });
            
            if (success) {
                if (statusDiv) statusDiv.classList.remove('loading');
                if (statusDiv) statusDiv.classList.add('ready');
                
                this.uiManager.showNotification('AI Dungeon Master initialized successfully!', 'success');
                
                // Transition to character creation
                setTimeout(() => {
                    this.showScreen('character-creation');
                    this.populateCharacterClasses();
                }, 1500);
            } else {
                throw new Error('AI initialization failed');
            }
            
        } catch (error) {
            console.error('AI initialization error:', error);
            this.uiManager.showNotification('Failed to initialize AI. Please try again.', 'error');
            
            const startBtn = document.getElementById('startBtn');
            const statusDiv = document.getElementById('modelStatus');
            
            if (startBtn) startBtn.disabled = false;
            if (statusDiv) statusDiv.classList.remove('loading');
        }
    }

    /**
     * Update initialization progress
     */
    updateInitializationProgress(progress) {
        const progressBar = document.getElementById('progressBar');
        const progressFill = document.getElementById('progressFill');
        const statusText = document.getElementById('statusText');
        
        if (progressBar) progressBar.style.display = 'block';
        
        if (progressFill) {
            const percent = Math.round(progress.progress * 100);
            progressFill.style.width = percent + '%';
            progressFill.textContent = percent + '%';
        }
        
        if (statusText && progress.text) {
            statusText.textContent = progress.text;
        }
    }

    /**
     * Show a specific screen
     */
    showScreen(screenName) {
        // Hide all screens
        const screens = ['model-setup', 'character-creation', 'game-container'];
        screens.forEach(screen => {
            const element = document.getElementById(screen.replace('-', ''));
            if (element) {
                element.classList.remove('active');
                element.classList.add('hidden');
            }
        });
        
        // Show target screen
        const targetElement = document.getElementById(screenName.replace('-', ''));
        if (targetElement) {
            targetElement.classList.remove('hidden');
            targetElement.classList.add('active');
        }
        
        this.currentScreen = screenName;
    }

    /**
     * Show error message
     */
    showError(message) {
        this.uiManager.showModal('Error', `
            <div class="text-center">
                <div class="text-error mb-md">❌ ${message}</div>
                <button class="btn btn-primary" onclick="location.reload()">Reload Game</button>
            </div>
        `);
    }

    /**
     * Check for saved game
     */
    async checkForSavedGame() {
        const savedGame = await this.saveManager.loadGame();
        if (savedGame) {
            console.log('Found saved game from', new Date(savedGame.timestamp).toLocaleString());
            // TODO: Implement saved game restoration
        }
    }

    /**
     * Update class description when character class changes
     */
    updateClassDescription() {
        const characterClass = document.getElementById('characterClass');
        const classDescription = document.getElementById('classDescription');

        if (!characterClass || !classDescription) return;

        const selectedClass = characterClass.value;
        if (!selectedClass) {
            classDescription.innerHTML = '<p class="text-medium">Select a class to see its description and abilities</p>';
            return;
        }

        const classConfig = getCharacterClass(selectedClass);
        if (!classConfig) return;

        classDescription.classList.add('active');
        classDescription.innerHTML = `
            <h4 class="text-primary mb-sm">${classConfig.name}</h4>
            <p class="text-medium mb-md">${classConfig.description}</p>

            <div class="mb-md">
                <h5 class="text-warning mb-sm">Primary Stat: ${classConfig.primaryStat}</h5>
                <div class="stats-preview">
                    ${Object.entries(classConfig.baseStats).map(([stat, value]) =>
                        `<span class="badge badge-secondary">${stat}: ${value}</span>`
                    ).join(' ')}
                </div>
            </div>

            <div class="mb-md">
                <h5 class="text-success mb-sm">Starting Abilities:</h5>
                <div class="abilities-preview">
                    ${classConfig.abilities.map(ability =>
                        `<span class="badge badge-success">${ability}</span>`
                    ).join(' ')}
                </div>
            </div>

            <div class="mb-md">
                <h5 class="text-warning mb-sm">Starting Items:</h5>
                <div class="items-preview">
                    ${classConfig.startingItems.map(item =>
                        `<span class="badge badge-warning">${item}</span>`
                    ).join(' ')}
                </div>
            </div>

            <div class="lore-preview">
                <h5 class="text-dim mb-sm">Lore:</h5>
                <p class="text-dim" style="font-style: italic; font-size: 0.9em;">${classConfig.lore}</p>
            </div>
        `;

        this.updateCharacterPreview();
    }

    /**
     * Update character preview
     */
    updateCharacterPreview() {
        const playerName = document.getElementById('playerName');
        const characterClass = document.getElementById('characterClass');
        const characterDesc = document.getElementById('characterDesc');
        const characterPreview = document.getElementById('characterPreview');

        if (!playerName || !characterClass || !characterPreview) return;

        const name = playerName.value.trim();
        const selectedClass = characterClass.value;
        const description = characterDesc.value.trim();

        if (!name || !selectedClass) {
            characterPreview.style.display = 'none';
            return;
        }

        const classConfig = getCharacterClass(selectedClass);
        if (!classConfig) return;

        characterPreview.style.display = 'block';

        const previewContent = characterPreview.querySelector('.preview-content');
        previewContent.innerHTML = `
            <div class="character-preview-header mb-md">
                <h4 class="text-primary">${name}</h4>
                <p class="text-medium">${selectedClass}</p>
                <p class="text-dim">${description || classConfig.description}</p>
            </div>

            <div class="preview-stats">
                ${Object.entries(classConfig.baseStats).map(([stat, value]) => `
                    <div class="preview-stat">
                        <span class="preview-stat-label">${stat}</span>
                        <span class="preview-stat-value">${value}</span>
                    </div>
                `).join('')}
            </div>
        `;
    }

    /**
     * Handle character creation
     */
    async handleCharacterCreation() {
        const playerName = document.getElementById('playerName');
        const characterClass = document.getElementById('characterClass');
        const characterDesc = document.getElementById('characterDesc');

        if (!playerName || !characterClass) return;

        const name = playerName.value.trim();
        const selectedClass = characterClass.value;
        const description = characterDesc.value.trim();

        if (!name) {
            this.uiManager.showNotification('Please enter a character name.', 'warning');
            playerName.focus();
            return;
        }

        if (!selectedClass) {
            this.uiManager.showNotification('Please select a character class.', 'warning');
            characterClass.focus();
            return;
        }

        try {
            // Create character
            this.currentPlayer = this.characterManager.createCharacter(name, selectedClass, description);
            this.characterManager.setActiveCharacter(this.currentPlayer);

            // Initialize game state
            this.gameEngine.gameState.players.set(this.currentPlayer.id, this.currentPlayer);

            // Start the game
            this.handleGameStart();

        } catch (error) {
            console.error('Character creation error:', error);
            this.uiManager.showNotification('Failed to create character. Please try again.', 'error');
        }
    }

    /**
     * Handle game start
     */
    async handleGameStart() {
        try {
            // Show game screen
            this.showScreen('game-container');

            // Update UI with character info
            this.uiManager.updateCharacterInfo(this.currentPlayer);
            this.uiManager.updateInventory(this.currentPlayer);
            this.uiManager.updateLocation(this.gameEngine.gameState.location, {
                chapter: this.gameEngine.gameState.chapter,
                danger: 'Safe'
            });

            // Update AI status
            this.uiManager.updateAIStatus(this.aiManager.getStatus());

            // Set up game engine event listeners
            this.setupGameEngineEvents();

            // Add welcome message
            setTimeout(() => {
                const welcomeMessage = `Welcome, ${this.currentPlayer.name} the ${this.currentPlayer.class}. You stand before the Crystal Gates of Shambhala, where amethyst pillars reach toward infinity. The gates hum with recognition as your ${this.getClassAttribute(this.currentPlayer.class)} resonates with their ancient power. What is your first action in this realm beneath realms?`;

                this.uiManager.addMessage('dm', 'The Eternal Keeper', welcomeMessage);
            }, 1000);

            // Focus on input
            const messageInput = document.getElementById('messageInput');
            if (messageInput) {
                messageInput.focus();
            }

        } catch (error) {
            console.error('Game start error:', error);
            this.uiManager.showNotification('Failed to start game. Please try again.', 'error');
        }
    }

    /**
     * Setup game engine event listeners
     */
    setupGameEngineEvents() {
        this.gameEngine.on('gameStateChanged', (data) => {
            this.uiManager.updateCharacterInfo(this.currentPlayer);
            this.uiManager.updateInventory(this.currentPlayer);
        });

        this.gameEngine.on('locationChanged', (data) => {
            this.uiManager.updateLocation(data.to, {
                chapter: this.gameEngine.gameState.chapter,
                danger: this.calculateLocationDanger(data.to)
            });
        });

        this.gameEngine.on('itemAdded', (data) => {
            this.uiManager.showNotification(`Found: ${data.item}`, 'success');
        });

        this.gameEngine.on('questCompleted', (data) => {
            this.uiManager.showNotification(`Quest completed: ${data.quest.title}`, 'success');
        });

        this.gameEngine.on('randomEvent', (data) => {
            this.uiManager.addMessage('environment', 'The Inner Realm', data.event);
        });
    }

    /**
     * Handle sending messages
     */
    async handleSendMessage() {
        const messageInput = document.getElementById('messageInput');
        if (!messageInput || !this.currentPlayer) return;

        const message = messageInput.value.trim();
        if (!message) return;

        if (this.gameEngine.isProcessing) {
            this.uiManager.showNotification('Please wait for the current action to complete.', 'warning');
            return;
        }

        try {
            // Clear input
            messageInput.value = '';
            this.updateCharacterCount(messageInput);

            // Show typing indicator
            this.uiManager.showTypingIndicator();

            // Add player message
            this.uiManager.addMessage('player', this.currentPlayer.name, message);

            // Process action through game engine
            const result = await this.gameEngine.processPlayerAction(this.currentPlayer, message);

            // Hide typing indicator
            this.uiManager.hideTypingIndicator();

            if (result.success) {
                // Add DM response
                this.uiManager.addMessage('dm', 'The Eternal Keeper', result.response);
            } else {
                this.uiManager.showNotification(result.message, 'error');
            }

        } catch (error) {
            console.error('Message send error:', error);
            this.uiManager.hideTypingIndicator();
            this.uiManager.showNotification('Failed to process action. Please try again.', 'error');
        }

        // Focus back on input
        messageInput.focus();
    }

    /**
     * Handle quick actions
     */
    handleQuickAction(action) {
        const messageInput = document.getElementById('messageInput');
        if (messageInput) {
            messageInput.value = action;
            messageInput.focus();
        }
    }

    /**
     * Update character count display
     */
    updateCharacterCount(input) {
        this.uiManager.updateCharacterCount(input);
    }

    /**
     * Handle keyboard shortcuts
     */
    handleKeyboardShortcuts(e) {
        // Escape key - close modals
        if (e.key === 'Escape') {
            this.uiManager.hideModal();
        }

        // Ctrl/Cmd + S - Save game
        if ((e.ctrlKey || e.metaKey) && e.key === 's') {
            e.preventDefault();
            this.handleSaveGame();
        }

        // Ctrl/Cmd + L - Load game
        if ((e.ctrlKey || e.metaKey) && e.key === 'l') {
            e.preventDefault();
            this.handleLoadGame();
        }
    }

    /**
     * Handle save game
     */
    async handleSaveGame() {
        if (!this.currentPlayer) return;

        try {
            await this.gameEngine.saveGame();
            this.uiManager.showNotification('Game saved successfully!', 'success');
        } catch (error) {
            console.error('Save error:', error);
            this.uiManager.showNotification('Failed to save game.', 'error');
        }
    }

    /**
     * Handle load game
     */
    async handleLoadGame() {
        // TODO: Implement load game UI
        this.uiManager.showNotification('Load game feature coming soon!', 'info');
    }

    /**
     * Handle before unload
     */
    handleBeforeUnload(e) {
        if (this.currentPlayer && this.gameEngine) {
            // Auto-save before leaving
            this.gameEngine.autoSave();
        }
    }

    /**
     * Handle window resize
     */
    handleResize() {
        if (this.uiManager) {
            this.uiManager.handleResize();
        }
    }

    /**
     * Get class attribute for flavor text
     */
    getClassAttribute(characterClass) {
        const attributes = {
            'Crystal Keeper': 'crystalline aura',
            'Vril Engineer': 'energy field',
            'Lemurian Scholar': 'ancient wisdom',
            'Atlantean Warrior': 'warrior spirit',
            'Inner Earth Scout': 'heightened senses',
            'Light Weaver': 'luminous essence'
        };

        return attributes[characterClass] || 'inner light';
    }

    /**
     * Calculate location danger level
     */
    calculateLocationDanger(locationName) {
        const dangerLevels = {
            'Crystal Gates of Shambhala': 'Safe',
            'Shambhala Central Plaza': 'Safe',
            'Hall of Records': 'Low',
            'Telos Beneath Mt. Shasta': 'Low',
            'Vril Power Station': 'Medium',
            'Deep Tunnels': 'High',
            'Forbidden Chambers': 'Extreme'
        };

        return dangerLevels[locationName] || 'Medium';
    }
}

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', async () => {
    console.log(`
╔══════════════════════════════════════════════════════╗
║     AGARTHA: AI-POWERED RPG - PRODUCTION READY      ║
╠══════════════════════════════════════════════════════╣
║  🤖 Powered by Web-LLM (runs entirely in browser)   ║
║  🎮 Full RPG mechanics with inventory & stats       ║
║  ✨ Dynamic AI dungeon master                       ║
║  🌐 Ready for deployment on any web server          ║
║  💾 Auto-save functionality                         ║
║  📱 Mobile responsive design                        ║
║  🔮 Rich lore based on real mythology               ║
╚══════════════════════════════════════════════════════╝
    `);
    
    // Create and initialize the application
    window.agarthaApp = new AgarthaApp();
    await window.agarthaApp.initialize();
});

// Export for module usage
export { AgarthaApp };
