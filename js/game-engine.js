/**
 * AGARTHA RPG - GAME ENGINE
 * Core game logic and state management
 */

import { CONFIG } from './config.js';
import { LORE, getContextualLore } from './lore.js';

/**
 * Game Engine Class
 * Manages game state, mechanics, and core gameplay systems
 */
export class GameEngine {
    constructor(ai<PERSON><PERSON><PERSON>, character<PERSON>anager, saveManager) {
        this.aiManager = aiManager;
        this.characterManager = characterManager;
        this.saveManager = saveManager;
        
        // Game state
        this.gameState = {
            location: 'Crystal Gates of Shambhala',
            chapter: 1,
            timeOfDay: 'eternal twilight',
            players: new Map(),
            npcs: new Map(),
            inventory: new Map(),
            messageHistory: [],
            worldState: {
                discovered: ['Crystal Gates of Shambhala'],
                npcsmet: [],
                questsActive: [],
                questsCompleted: [],
                secrets: [],
                artifacts: []
            },
            gameStats: {
                actionsPerformed: 0,
                locationsVisited: 1,
                npcsEncountered: 0,
                questsCompleted: 0,
                startTime: Date.now(),
                playTime: 0
            }
        };
        
        // Game mechanics
        this.isProcessing = false;
        this.autoSaveTimer = null;
        this.lastActionTime = Date.now();
        
        // Event system
        this.eventListeners = new Map();
        
        this.initializeGameSystems();
    }

    /**
     * Initialize game systems
     */
    initializeGameSystems() {
        // Start auto-save timer
        if (CONFIG.game.autoSaveEnabled) {
            this.autoSaveTimer = setInterval(() => {
                this.autoSave();
            }, CONFIG.game.saveInterval);
        }
        
        // Initialize world state
        this.initializeWorld();
        
        console.log('🎮 Game Engine initialized');
    }

    /**
     * Initialize the game world
     */
    initializeWorld() {
        // Set up initial location
        const initialLocation = CONFIG.locations['Crystal Gates of Shambhala'];
        if (initialLocation) {
            this.gameState.location = initialLocation.name;
            this.gameState.worldState.discovered = [initialLocation.name];
        }
        
        // Initialize NPCs
        this.initializeNPCs();
        
        // Set up initial quests
        this.initializeQuests();
    }

    /**
     * Initialize NPCs in the world
     */
    initializeNPCs() {
        const initialNPCs = [
            {
                id: 'gate_guardian_zephyr',
                name: 'Gate Guardian Zephyr',
                location: 'Crystal Gates of Shambhala',
                type: 'guardian',
                personality: 'wise, ancient, protective',
                knowledge: ['ancient_history', 'portal_mechanics', 'visitor_guidance'],
                dialogue: {
                    greeting: "Welcome, surface dweller. I sense the awakening within you. The gates recognize your resonance.",
                    help: "The Crystal Gates respond to pure intention. Focus your mind and speak your purpose.",
                    lore: "These gates have stood for millennia, built by the first refugees from the surface catastrophes."
                }
            }
        ];
        
        initialNPCs.forEach(npc => {
            this.gameState.npcs.set(npc.id, npc);
        });
    }

    /**
     * Initialize starting quests
     */
    initializeQuests() {
        const initialQuests = [
            {
                id: 'first_steps',
                title: 'First Steps in Agartha',
                description: 'Learn the basics of navigating the inner realm',
                objectives: [
                    'Speak with Gate Guardian Zephyr',
                    'Examine the Crystal Gates',
                    'Enter Shambhala Central Plaza'
                ],
                rewards: {
                    experience: 100,
                    items: ['Newcomer\'s Crystal']
                },
                status: 'active'
            }
        ];
        
        this.gameState.worldState.questsActive = initialQuests;
    }

    /**
     * Process player action
     */
    async processPlayerAction(player, action) {
        if (this.isProcessing) {
            return { success: false, message: 'Please wait for the current action to complete.' };
        }

        this.isProcessing = true;
        this.lastActionTime = Date.now();

        try {
            // Update game stats
            this.gameState.gameStats.actionsPerformed++;
            
            // Add to message history
            this.addToMessageHistory('player', player.name, action);
            
            // Process the action through AI
            const context = this.buildActionContext(player, action);
            const response = await this.aiManager.generateResponse(action, player, context);
            
            // Parse response for game elements
            const gameEvents = this.parseResponseForGameElements(response);
            
            // Apply game events
            await this.applyGameEvents(gameEvents, player);
            
            // Add DM response to history
            this.addToMessageHistory('dm', 'The Eternal Keeper', response);
            
            // Check for quest updates
            this.updateQuests(gameEvents, player);
            
            // Trigger random events
            this.checkForRandomEvents();
            
            // Update play time
            this.updatePlayTime();
            
            return {
                success: true,
                response: response,
                gameEvents: gameEvents
            };
            
        } catch (error) {
            console.error('Error processing player action:', error);
            return {
                success: false,
                message: 'The connection to the Akashic Records was interrupted. Please try again.'
            };
        } finally {
            this.isProcessing = false;
        }
    }

    /**
     * Build context for AI action processing
     */
    buildActionContext(player, action) {
        const currentLocation = CONFIG.locations[this.gameState.location];
        const characterLore = this.characterManager.getCharacterLore(player.class);
        const contextualLore = getContextualLore(this.gameState.location, player);
        
        return {
            location: currentLocation,
            character: player,
            characterLore: characterLore,
            worldState: this.gameState.worldState,
            recentHistory: this.getRecentHistory(5),
            activeQuests: this.gameState.worldState.questsActive,
            nearbyNPCs: this.getNearbyNPCs(),
            availableItems: this.getAvailableItems(),
            gameStats: this.gameState.gameStats,
            contextualLore: contextualLore
        };
    }

    /**
     * Parse AI response for game elements
     */
    parseResponseForGameElements(response) {
        const events = {
            locationChange: null,
            itemsFound: [],
            npcsEncountered: [],
            questUpdates: [],
            statChanges: {},
            discoveries: [],
            combatEvents: [],
            dialogueEvents: []
        };

        // Extract location changes
        const locationMatch = response.match(/You (?:enter|arrive at|reach|travel to) (?:the )?(.+?)[\.\,\!]/i);
        if (locationMatch) {
            const newLocation = locationMatch[1].trim();
            if (CONFIG.locations[newLocation]) {
                events.locationChange = newLocation;
            }
        }

        // Extract items found
        const itemMatches = response.matchAll(/You (?:find|discover|receive|obtain) (?:a |an |the )?(.+?)[\.\,\!]/gi);
        for (const match of itemMatches) {
            events.itemsFound.push(match[1].trim());
        }

        // Extract NPC encounters
        const npcMatches = response.matchAll(/(?:meet|encounter|see|speak with) (?:a |an |the )?(.+?) (?:who|that|says)/gi);
        for (const match of npcMatches) {
            events.npcsEncountered.push(match[1].trim());
        }

        // Extract dice rolls and outcomes
        const rollMatches = response.matchAll(/\[Roll: ?(\d+)\]/gi);
        for (const match of rollMatches) {
            const roll = parseInt(match[1]);
            events.combatEvents.push({ type: 'dice_roll', value: roll });
        }

        // Extract stat changes (health, vril, etc.)
        const healthMatch = response.match(/(?:lose|gain|restore) (\d+) (?:health|life|vitality)/i);
        if (healthMatch) {
            const amount = parseInt(healthMatch[1]);
            const isGain = response.includes('gain') || response.includes('restore');
            events.statChanges.health = isGain ? amount : -amount;
        }

        const vrilMatch = response.match(/(?:lose|gain|channel) (\d+) (?:vril|energy)/i);
        if (vrilMatch) {
            const amount = parseInt(vrilMatch[1]);
            const isGain = response.includes('gain') || response.includes('channel');
            events.statChanges.vril = isGain ? amount : -amount;
        }

        return events;
    }

    /**
     * Apply game events to the world state
     */
    async applyGameEvents(events, player) {
        // Handle location changes
        if (events.locationChange) {
            this.changeLocation(events.locationChange);
        }

        // Handle items found
        events.itemsFound.forEach(item => {
            this.addItemToInventory(player, item);
        });

        // Handle NPC encounters
        events.npcsEncountered.forEach(npcName => {
            this.recordNPCEncounter(npcName);
        });

        // Handle stat changes
        if (Object.keys(events.statChanges).length > 0) {
            this.characterManager.updateCharacterStats(player, events.statChanges);
        }

        // Handle discoveries
        events.discoveries.forEach(discovery => {
            this.recordDiscovery(discovery);
        });

        // Emit events for UI updates
        this.emit('gameStateChanged', {
            gameState: this.gameState,
            events: events
        });
    }

    /**
     * Change current location
     */
    changeLocation(newLocation) {
        const oldLocation = this.gameState.location;
        this.gameState.location = newLocation;
        
        // Add to discovered locations
        if (!this.gameState.worldState.discovered.includes(newLocation)) {
            this.gameState.worldState.discovered.push(newLocation);
            this.gameState.gameStats.locationsVisited++;
        }
        
        this.emit('locationChanged', {
            from: oldLocation,
            to: newLocation
        });
    }

    /**
     * Add item to player inventory
     */
    addItemToInventory(player, itemName) {
        const playerId = player.name;
        if (!this.gameState.inventory.has(playerId)) {
            this.gameState.inventory.set(playerId, []);
        }
        
        const playerInventory = this.gameState.inventory.get(playerId);
        if (playerInventory.length < 12) { // Max inventory size
            playerInventory.push({
                name: itemName,
                type: this.determineItemType(itemName),
                acquired: Date.now()
            });
            
            this.emit('itemAdded', {
                player: playerId,
                item: itemName
            });
        }
    }

    /**
     * Determine item type based on name
     */
    determineItemType(itemName) {
        const name = itemName.toLowerCase();
        
        if (name.includes('crystal')) return 'crystal';
        if (name.includes('scroll') || name.includes('tome')) return 'knowledge';
        if (name.includes('key') || name.includes('device')) return 'tool';
        if (name.includes('armor') || name.includes('shield')) return 'armor';
        if (name.includes('weapon') || name.includes('blade')) return 'weapon';
        if (name.includes('potion') || name.includes('elixir')) return 'consumable';
        
        return 'misc';
    }

    /**
     * Record NPC encounter
     */
    recordNPCEncounter(npcName) {
        if (!this.gameState.worldState.npcsmet.includes(npcName)) {
            this.gameState.worldState.npcsmet.push(npcName);
            this.gameState.gameStats.npcsEncountered++;
        }
    }

    /**
     * Update quest progress
     */
    updateQuests(events, player) {
        // Check active quests for completion criteria
        this.gameState.worldState.questsActive.forEach(quest => {
            if (quest.status === 'active') {
                // Check if any objectives are completed by recent events
                quest.objectives.forEach((objective, index) => {
                    if (!quest.completedObjectives) quest.completedObjectives = [];
                    
                    if (!quest.completedObjectives.includes(index)) {
                        if (this.checkObjectiveCompletion(objective, events, player)) {
                            quest.completedObjectives.push(index);
                        }
                    }
                });
                
                // Check if quest is complete
                if (quest.completedObjectives && 
                    quest.completedObjectives.length === quest.objectives.length) {
                    this.completeQuest(quest, player);
                }
            }
        });
    }

    /**
     * Check if a quest objective is completed
     */
    checkObjectiveCompletion(objective, events, player) {
        const obj = objective.toLowerCase();
        
        // Location-based objectives
        if (obj.includes('enter') && events.locationChange) {
            return obj.includes(events.locationChange.toLowerCase());
        }
        
        // NPC interaction objectives
        if (obj.includes('speak with') || obj.includes('talk to')) {
            return events.npcsEncountered.some(npc => 
                obj.includes(npc.toLowerCase())
            );
        }
        
        // Item collection objectives
        if (obj.includes('find') || obj.includes('collect')) {
            return events.itemsFound.some(item => 
                obj.includes(item.toLowerCase())
            );
        }
        
        return false;
    }

    /**
     * Complete a quest
     */
    completeQuest(quest, player) {
        quest.status = 'completed';
        quest.completedAt = Date.now();
        
        // Move to completed quests
        this.gameState.worldState.questsCompleted.push(quest);
        this.gameState.worldState.questsActive = this.gameState.worldState.questsActive.filter(q => q.id !== quest.id);
        
        // Award rewards
        if (quest.rewards) {
            if (quest.rewards.experience) {
                this.characterManager.awardExperience(player, quest.rewards.experience);
            }
            
            if (quest.rewards.items) {
                quest.rewards.items.forEach(item => {
                    this.addItemToInventory(player, item);
                });
            }
        }
        
        this.gameState.gameStats.questsCompleted++;
        
        this.emit('questCompleted', {
            quest: quest,
            player: player
        });
    }

    /**
     * Check for random events
     */
    checkForRandomEvents() {
        if (Math.random() > 0.92) { // 8% chance
            setTimeout(() => {
                this.triggerRandomEvent();
            }, Math.random() * 10000 + 5000); // 5-15 seconds delay
        }
    }

    /**
     * Trigger a random environmental event
     */
    triggerRandomEvent() {
        const events = [
            "The crystalline walls pulse with renewed energy, casting dancing shadows.",
            "A distant harmonic tone echoes through the caverns, resonating in your bones.",
            "The air shimmers as reality briefly fluctuates around you.",
            "You sense an ancient presence observing from the ethereal realm.",
            "The vril currents suddenly intensify, making your skin tingle.",
            "Whispers in an unknown language drift on the underground breeze.",
            "A faint aurora of colored light plays across the cavern ceiling.",
            "The ground trembles slightly as massive machinery operates far below."
        ];
        
        const event = events[Math.floor(Math.random() * events.length)];
        
        this.addToMessageHistory('environment', 'The Inner Realm', event);
        
        this.emit('randomEvent', {
            event: event,
            timestamp: Date.now()
        });
    }

    /**
     * Add message to history
     */
    addToMessageHistory(type, author, content) {
        const message = {
            type,
            author,
            content,
            timestamp: Date.now(),
            location: this.gameState.location
        };
        
        this.gameState.messageHistory.push(message);
        
        // Limit history size
        if (this.gameState.messageHistory.length > CONFIG.game.maxMessages) {
            this.gameState.messageHistory.shift();
        }
    }

    /**
     * Get recent message history
     */
    getRecentHistory(count = 5) {
        return this.gameState.messageHistory.slice(-count);
    }

    /**
     * Get NPCs in current location
     */
    getNearbyNPCs() {
        return Array.from(this.gameState.npcs.values()).filter(npc => 
            npc.location === this.gameState.location
        );
    }

    /**
     * Get available items in current location
     */
    getAvailableItems() {
        const location = CONFIG.locations[this.gameState.location];
        return location ? location.items || [] : [];
    }

    /**
     * Update play time
     */
    updatePlayTime() {
        this.gameState.gameStats.playTime = Date.now() - this.gameState.gameStats.startTime;
    }

    /**
     * Auto-save game state
     */
    async autoSave() {
        try {
            await this.saveManager.saveGame({
                gameState: this.gameState,
                timestamp: Date.now(),
                version: CONFIG.game.version
            });
        } catch (error) {
            console.warn('Auto-save failed:', error);
        }
    }

    /**
     * Event system methods
     */
    on(event, callback) {
        if (!this.eventListeners.has(event)) {
            this.eventListeners.set(event, []);
        }
        this.eventListeners.get(event).push(callback);
    }

    emit(event, data) {
        if (this.eventListeners.has(event)) {
            this.eventListeners.get(event).forEach(callback => {
                try {
                    callback(data);
                } catch (error) {
                    console.error(`Error in event listener for ${event}:`, error);
                }
            });
        }
    }

    /**
     * Get current game state
     */
    getGameState() {
        return { ...this.gameState };
    }

    /**
     * Cleanup resources
     */
    destroy() {
        if (this.autoSaveTimer) {
            clearInterval(this.autoSaveTimer);
        }
        
        this.eventListeners.clear();
    }
}
