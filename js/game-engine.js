/**
 * AGARTHA RPG - GAME ENGINE
 * Core game logic and state management
 */

import { CONFIG } from './config.js';
import { LORE, getContextualLore } from './lore.js';

/**
 * Game Engine Class
 * Manages game state, mechanics, and core gameplay systems
 */
export class GameEngine {
    constructor(ai<PERSON><PERSON><PERSON>, character<PERSON>anager, saveManager) {
        this.aiManager = aiManager;
        this.characterManager = characterManager;
        this.saveManager = saveManager;
        
        // Game state
        this.gameState = {
            location: 'Crystal Gates of Shambhala',
            chapter: 1,
            timeOfDay: 'eternal twilight',
            players: new Map(),
            npcs: new Map(),
            inventory: new Map(),
            messageHistory: [],
            worldState: {
                discovered: ['Crystal Gates of Shambhala'],
                npcsmet: [],
                questsActive: [],
                questsCompleted: [],
                secrets: [],
                artifacts: []
            },
            gameStats: {
                actionsPerformed: 0,
                locationsVisited: 1,
                npcsEncountered: 0,
                questsCompleted: 0,
                startTime: Date.now(),
                playTime: 0
            }
        };
        
        // Game mechanics
        this.isProcessing = false;
        this.autoSaveTimer = null;
        this.lastActionTime = Date.now();
        
        // Event system
        this.eventListeners = new Map();
        
        this.initializeGameSystems();
    }

    /**
     * Initialize game systems
     */
    initializeGameSystems() {
        // Start auto-save timer
        if (CONFIG.game.autoSaveEnabled) {
            this.autoSaveTimer = setInterval(() => {
                this.autoSave();
            }, CONFIG.game.saveInterval);
        }
        
        // Initialize world state
        this.initializeWorld();
        
        console.log('🎮 Game Engine initialized');
    }

    /**
     * Initialize the game world
     */
    initializeWorld() {
        // Set up initial location
        const initialLocation = CONFIG.locations['Crystal Gates of Shambhala'];
        if (initialLocation) {
            this.gameState.location = initialLocation.name;
            this.gameState.worldState.discovered = [initialLocation.name];
        }
        
        // Initialize NPCs
        this.initializeNPCs();
        
        // Set up initial quests
        this.initializeQuests();
    }

    /**
     * Initialize NPCs in the world
     */
    initializeNPCs() {
        const initialNPCs = [
            {
                id: 'gate_guardian_zephyr',
                name: 'Gate Guardian Zephyr',
                location: 'Crystal Gates of Shambhala',
                type: 'guardian',
                personality: 'wise, ancient, protective',
                knowledge: ['ancient_history', 'portal_mechanics', 'visitor_guidance'],
                dialogue: {
                    greeting: "Welcome, surface dweller. I sense the awakening within you. The gates recognize your resonance.",
                    help: "The Crystal Gates respond to pure intention. Focus your mind and speak your purpose.",
                    lore: "These gates have stood for millennia, built by the first refugees from the surface catastrophes."
                }
            },
            {
                id: 'elder_thoth',
                name: 'Elder Thoth',
                location: 'Hall of Records',
                type: 'scholar',
                personality: 'wise, patient, knowledgeable',
                knowledge: ['ancient_civilizations', 'akashic_records', 'cosmic_history'],
                dialogue: {
                    greeting: "Greetings, seeker. I am Thoth, keeper of the eternal records. What knowledge do you seek?",
                    help: "The Hall of Records contains the complete history of Earth and beyond. Ask, and you shall receive wisdom.",
                    lore: "I have witnessed the rise and fall of civilizations, the great migrations, and the cosmic cycles."
                }
            },
            {
                id: 'high_priestess_adama',
                name: 'High Priestess Adama',
                location: 'Telos Beneath Mt. Shasta',
                type: 'priestess',
                personality: 'serene, telepathic, compassionate',
                knowledge: ['lemurian_wisdom', 'telepathy', 'consciousness_evolution'],
                dialogue: {
                    greeting: "Welcome, child of the surface. I am Adama, and I have been expecting you. Your thoughts reach me clearly.",
                    help: "The path of consciousness requires patience and practice. Let me guide you in the ancient ways.",
                    lore: "We Telosians are the descendants of Lemuria, keepers of the telepathic arts and guardians of inner wisdom."
                }
            },
            {
                id: 'vril_engineer_kael',
                name: 'Vril Engineer Kael',
                location: 'Vril Power Station',
                type: 'engineer',
                personality: 'technical, precise, innovative',
                knowledge: ['vril_technology', 'energy_systems', 'atlantean_science'],
                dialogue: {
                    greeting: "Ah, a newcomer to the energy arts! I am Kael, master of vril manipulation. Ready to learn?",
                    help: "Vril is the fundamental force of life itself. With proper training, you can channel it safely.",
                    lore: "The Atlanteans first discovered vril, but we have perfected its use here in Agartha."
                }
            }
        ];

        initialNPCs.forEach(npc => {
            this.gameState.npcs.set(npc.id, npc);
        });
    }

    /**
     * Initialize starting quests
     */
    initializeQuests() {
        const questChain = [
            // Chapter 1: Arrival
            {
                id: 'first_steps',
                title: 'First Steps in Agartha',
                description: 'Learn the basics of navigating the inner realm',
                chapter: 1,
                objectives: [
                    'Speak with Gate Guardian Zephyr',
                    'Examine the Crystal Gates',
                    'Enter Shambhala Central Plaza'
                ],
                rewards: {
                    experience: 100,
                    items: ['Newcomer\'s Crystal']
                },
                status: 'active',
                nextQuest: 'city_exploration'
            },

            // Chapter 2: City Exploration
            {
                id: 'city_exploration',
                title: 'Exploring the Great City',
                description: 'Discover the wonders of Shambhala Central Plaza',
                chapter: 2,
                objectives: [
                    'Visit the Hall of Records',
                    'Meet Elder Thoth',
                    'Learn about the three civilizations',
                    'Acquire a Communication Crystal'
                ],
                rewards: {
                    experience: 250,
                    items: ['Communication Crystal', 'Ancient Map']
                },
                status: 'locked',
                nextQuest: 'vril_awakening'
            },

            // Chapter 3: Vril Awakening
            {
                id: 'vril_awakening',
                title: 'Awakening the Vril Within',
                description: 'Learn to harness the fundamental life force',
                chapter: 3,
                objectives: [
                    'Visit the Vril Power Station',
                    'Complete the Vril Attunement Ritual',
                    'Channel vril energy successfully',
                    'Speak with a Vril Engineer'
                ],
                rewards: {
                    experience: 400,
                    items: ['Vril Conductor', 'Energy Amplifier']
                },
                status: 'locked',
                nextQuest: 'lemurian_wisdom'
            },

            // Chapter 4: Lemurian Wisdom
            {
                id: 'lemurian_wisdom',
                title: 'Seeking Lemurian Wisdom',
                description: 'Journey to Telos and learn from the Telosians',
                chapter: 4,
                objectives: [
                    'Travel to Telos beneath Mt. Shasta',
                    'Meet High Priestess Adama',
                    'Learn telepathic communication',
                    'Access the Akashic Records'
                ],
                rewards: {
                    experience: 600,
                    items: ['Telepathic Amplifier', 'Memory Stone']
                },
                status: 'locked',
                nextQuest: 'atlantean_legacy'
            },

            // Chapter 5: Atlantean Legacy
            {
                id: 'atlantean_legacy',
                title: 'Uncovering Atlantean Secrets',
                description: 'Discover the advanced technology of lost Atlantis',
                chapter: 5,
                objectives: [
                    'Find the hidden Atlantean laboratory',
                    'Activate ancient crystal technology',
                    'Learn about the Great Cataclysm',
                    'Retrieve Atlantean artifacts'
                ],
                rewards: {
                    experience: 800,
                    items: ['Atlantean Crystal Core', 'Genetic Scanner']
                },
                status: 'locked',
                nextQuest: 'inner_sun_pilgrimage'
            },

            // Chapter 6: The Inner Sun
            {
                id: 'inner_sun_pilgrimage',
                title: 'Pilgrimage to the Central Sun',
                description: 'Journey to the heart of Agartha',
                chapter: 6,
                objectives: [
                    'Prepare for the journey to the Central Sun',
                    'Navigate the Deep Tunnels',
                    'Overcome the Guardian Trials',
                    'Reach the Chamber of the Smoky God'
                ],
                rewards: {
                    experience: 1200,
                    items: ['Solar Crystal', 'Guardian\'s Blessing']
                },
                status: 'locked',
                nextQuest: 'cosmic_revelation'
            },

            // Chapter 7: Cosmic Truth
            {
                id: 'cosmic_revelation',
                title: 'The Cosmic Revelation',
                description: 'Uncover the ultimate truth about Earth and humanity',
                chapter: 7,
                objectives: [
                    'Commune with the Central Sun consciousness',
                    'Learn about Earth\'s true history',
                    'Understand humanity\'s cosmic purpose',
                    'Receive the Star Seed activation'
                ],
                rewards: {
                    experience: 1500,
                    items: ['Star Seed', 'Cosmic Key']
                },
                status: 'locked',
                nextQuest: 'surface_mission'
            },

            // Chapter 8: The Return
            {
                id: 'surface_mission',
                title: 'Mission to the Surface',
                description: 'Return to the surface world as an awakened being',
                chapter: 8,
                objectives: [
                    'Prepare for the return journey',
                    'Establish communication networks',
                    'Begin the Great Awakening',
                    'Unite the surface and inner worlds'
                ],
                rewards: {
                    experience: 2000,
                    items: ['Ambassador\'s Seal', 'Unity Crystal']
                },
                status: 'locked',
                nextQuest: 'golden_age'
            },

            // Final Chapter: Golden Age
            {
                id: 'golden_age',
                title: 'Herald of the Golden Age',
                description: 'Usher in the new era of human consciousness',
                chapter: 9,
                objectives: [
                    'Establish the New Earth Council',
                    'Activate the global crystal network',
                    'Awaken humanity\'s dormant abilities',
                    'Complete the Great Work'
                ],
                rewards: {
                    experience: 3000,
                    items: ['Crown of Light', 'Master\'s Staff']
                },
                status: 'locked',
                isEndGame: true
            }
        ];

        this.gameState.worldState.questsActive = [questChain[0]]; // Start with first quest
        this.gameState.worldState.questChain = questChain;
        this.gameState.worldState.questsCompleted = [];
    }

    /**
     * Process player action
     */
    async processPlayerAction(player, action) {
        if (this.isProcessing) {
            return { success: false, message: 'Please wait for the current action to complete.' };
        }

        this.isProcessing = true;
        this.lastActionTime = Date.now();

        try {
            // Update game stats
            this.gameState.gameStats.actionsPerformed++;

            // Award experience for actions
            this.awardActionExperience(player, action);

            // Add to message history
            this.addToMessageHistory('player', player.name, action);
            
            // Process the action through AI
            const context = this.buildActionContext(player, action);
            const response = await this.aiManager.generateResponse(action, player, context);
            
            // Parse response for game elements
            const gameEvents = this.parseResponseForGameElements(response);
            
            // Apply game events
            await this.applyGameEvents(gameEvents, player);
            
            // Add DM response to history
            this.addToMessageHistory('dm', 'The Eternal Keeper', response);
            
            // Check for quest updates
            this.updateQuests(gameEvents, player);
            
            // Trigger random events
            this.checkForRandomEvents();
            
            // Update play time
            this.updatePlayTime();
            
            return {
                success: true,
                response: response,
                gameEvents: gameEvents
            };
            
        } catch (error) {
            console.error('Error processing player action:', error);
            return {
                success: false,
                message: 'The connection to the Akashic Records was interrupted. Please try again.'
            };
        } finally {
            this.isProcessing = false;
        }
    }

    /**
     * Build context for AI action processing
     */
    buildActionContext(player, action) {
        const currentLocation = CONFIG.locations[this.gameState.location];
        const characterLore = this.characterManager.getCharacterLore(player.class);
        const contextualLore = getContextualLore(this.gameState.location, player);
        
        return {
            location: currentLocation,
            character: player,
            characterLore: characterLore,
            worldState: this.gameState.worldState,
            recentHistory: this.getRecentHistory(5),
            activeQuests: this.gameState.worldState.questsActive,
            nearbyNPCs: this.getNearbyNPCs(),
            availableItems: this.getAvailableItems(),
            gameStats: { ...this.gameState.gameStats, chapter: this.gameState.chapter },
            contextualLore: contextualLore
        };
    }

    /**
     * Parse AI response for game elements
     */
    parseResponseForGameElements(response) {
        const events = {
            locationChange: null,
            itemsFound: [],
            npcsEncountered: [],
            questUpdates: [],
            statChanges: {},
            discoveries: [],
            combatEvents: [],
            dialogueEvents: []
        };

        // Extract location changes
        const locationMatch = response.match(/You (?:enter|arrive at|reach|travel to|visit) (?:the )?(.+?)[\.\,\!]/i);
        if (locationMatch) {
            const newLocation = locationMatch[1].trim();
            // Check both exact match and partial match
            const matchedLocation = Object.keys(CONFIG.locations).find(loc =>
                loc.toLowerCase().includes(newLocation.toLowerCase()) ||
                newLocation.toLowerCase().includes(loc.toLowerCase())
            );
            if (matchedLocation) {
                events.locationChange = matchedLocation;
            }
        }

        // Also check for location mentions in general
        Object.keys(CONFIG.locations).forEach(location => {
            if (response.toLowerCase().includes(location.toLowerCase())) {
                events.locationChange = location;
            }
        });

        // Extract items found
        const itemMatches = response.matchAll(/You (?:find|discover|receive|obtain) (?:a |an |the )?(.+?)[\.\,\!]/gi);
        for (const match of itemMatches) {
            events.itemsFound.push(match[1].trim());
        }

        // Extract NPC encounters
        const npcMatches = response.matchAll(/(?:meet|encounter|see|speak with|talk to|greet) (?:a |an |the )?(.+?) (?:who|that|says|tells|explains)/gi);
        for (const match of npcMatches) {
            events.npcsEncountered.push(match[1].trim());
        }

        // Also check for specific NPC names
        const npcNames = ['Zephyr', 'Thoth', 'Adama', 'Kael', 'Guardian', 'Elder', 'Keeper', 'Engineer'];
        npcNames.forEach(name => {
            if (response.toLowerCase().includes(name.toLowerCase())) {
                events.npcsEncountered.push(name);
            }
        });

        // Extract dice rolls and outcomes
        const rollMatches = response.matchAll(/\[Roll: ?(\d+)\]/gi);
        for (const match of rollMatches) {
            const roll = parseInt(match[1]);
            events.combatEvents.push({ type: 'dice_roll', value: roll });
        }

        // Extract stat changes (health, vril, etc.)
        const healthMatch = response.match(/(?:lose|gain|restore) (\d+) (?:health|life|vitality)/i);
        if (healthMatch) {
            const amount = parseInt(healthMatch[1]);
            const isGain = response.includes('gain') || response.includes('restore');
            events.statChanges.health = isGain ? amount : -amount;
        }

        const vrilMatch = response.match(/(?:lose|gain|channel) (\d+) (?:vril|energy)/i);
        if (vrilMatch) {
            const amount = parseInt(vrilMatch[1]);
            const isGain = response.includes('gain') || response.includes('channel');
            events.statChanges.vril = isGain ? amount : -amount;
        }

        return events;
    }

    /**
     * Apply game events to the world state
     */
    async applyGameEvents(events, player) {
        // Handle location changes
        if (events.locationChange) {
            this.changeLocation(events.locationChange);
        }

        // Handle items found
        events.itemsFound.forEach(item => {
            this.addItemToInventory(player, item);
        });

        // Handle NPC encounters
        events.npcsEncountered.forEach(npcName => {
            this.recordNPCEncounter(npcName);
        });

        // Handle stat changes
        if (Object.keys(events.statChanges).length > 0) {
            this.characterManager.updateCharacterStats(player, events.statChanges);
        }

        // Handle discoveries
        events.discoveries.forEach(discovery => {
            this.recordDiscovery(discovery);
        });

        // Emit events for UI updates
        this.emit('gameStateChanged', {
            gameState: this.gameState,
            events: events
        });
    }

    /**
     * Change current location
     */
    changeLocation(newLocation) {
        const oldLocation = this.gameState.location;
        this.gameState.location = newLocation;
        
        // Add to discovered locations
        if (!this.gameState.worldState.discovered.includes(newLocation)) {
            this.gameState.worldState.discovered.push(newLocation);
            this.gameState.gameStats.locationsVisited++;
        }
        
        this.emit('locationChanged', {
            from: oldLocation,
            to: newLocation
        });
    }

    /**
     * Add item to player inventory
     */
    addItemToInventory(player, itemName) {
        const playerId = player.name;
        if (!this.gameState.inventory.has(playerId)) {
            this.gameState.inventory.set(playerId, []);
        }
        
        const playerInventory = this.gameState.inventory.get(playerId);
        if (playerInventory.length < 12) { // Max inventory size
            playerInventory.push({
                name: itemName,
                type: this.determineItemType(itemName),
                acquired: Date.now()
            });
            
            this.emit('itemAdded', {
                player: playerId,
                item: itemName
            });
        }
    }

    /**
     * Determine item type based on name
     */
    determineItemType(itemName) {
        const name = itemName.toLowerCase();
        
        if (name.includes('crystal')) return 'crystal';
        if (name.includes('scroll') || name.includes('tome')) return 'knowledge';
        if (name.includes('key') || name.includes('device')) return 'tool';
        if (name.includes('armor') || name.includes('shield')) return 'armor';
        if (name.includes('weapon') || name.includes('blade')) return 'weapon';
        if (name.includes('potion') || name.includes('elixir')) return 'consumable';
        
        return 'misc';
    }

    /**
     * Record NPC encounter
     */
    recordNPCEncounter(npcName) {
        if (!this.gameState.worldState.npcsmet.includes(npcName)) {
            this.gameState.worldState.npcsmet.push(npcName);
            this.gameState.gameStats.npcsEncountered++;
        }
    }

    /**
     * Update quest progress
     */
    updateQuests(events, player) {
        // Check active quests for completion criteria
        this.gameState.worldState.questsActive.forEach(quest => {
            if (quest.status === 'active') {
                // Check if any objectives are completed by recent events
                quest.objectives.forEach((objective, index) => {
                    if (!quest.completedObjectives) quest.completedObjectives = [];
                    
                    if (!quest.completedObjectives.includes(index)) {
                        if (this.checkObjectiveCompletion(objective, events, player)) {
                            quest.completedObjectives.push(index);
                        }
                    }
                });
                
                // Check if quest is complete
                if (quest.completedObjectives && 
                    quest.completedObjectives.length === quest.objectives.length) {
                    this.completeQuest(quest, player);
                }
            }
        });
    }

    /**
     * Check if a quest objective is completed
     */
    checkObjectiveCompletion(objective, events, player) {
        const obj = objective.toLowerCase();
        const recentMessages = this.getRecentHistory(3);
        const recentText = recentMessages.map(m => m.content.toLowerCase()).join(' ');

        // Location-based objectives
        if ((obj.includes('enter') || obj.includes('visit') || obj.includes('travel to')) && events.locationChange) {
            return obj.includes(events.locationChange.toLowerCase()) ||
                   recentText.includes(events.locationChange.toLowerCase());
        }

        // NPC interaction objectives
        if (obj.includes('speak with') || obj.includes('talk to') || obj.includes('meet')) {
            const npcNames = ['zephyr', 'thoth', 'adama', 'guardian', 'elder', 'keeper'];
            return events.npcsEncountered.some(npc => obj.includes(npc.toLowerCase())) ||
                   npcNames.some(npc => obj.includes(npc) && recentText.includes(npc));
        }

        // Item collection objectives
        if (obj.includes('find') || obj.includes('collect') || obj.includes('acquire') || obj.includes('obtain')) {
            return events.itemsFound.some(item => obj.includes(item.toLowerCase())) ||
                   recentText.includes('crystal') && obj.includes('crystal') ||
                   recentText.includes('artifact') && obj.includes('artifact');
        }

        // Action-based objectives
        if (obj.includes('examine') || obj.includes('look at')) {
            return recentText.includes('examine') || recentText.includes('look') || recentText.includes('observe');
        }

        // Learning objectives
        if (obj.includes('learn') || obj.includes('understand')) {
            return recentText.includes('learn') || recentText.includes('understand') ||
                   recentText.includes('knowledge') || recentText.includes('wisdom');
        }

        // Ritual/action objectives
        if (obj.includes('ritual') || obj.includes('attunement') || obj.includes('channel')) {
            return recentText.includes('ritual') || recentText.includes('channel') ||
                   recentText.includes('energy') || recentText.includes('vril');
        }

        // Communication objectives
        if (obj.includes('telepathic') || obj.includes('communicate')) {
            return recentText.includes('telepathic') || recentText.includes('mind') ||
                   recentText.includes('communicate') || recentText.includes('thoughts');
        }

        // Generic completion check - if the objective keywords appear in recent messages
        const objectiveWords = obj.split(' ').filter(word => word.length > 3);
        const matchCount = objectiveWords.filter(word => recentText.includes(word)).length;

        return matchCount >= Math.ceil(objectiveWords.length * 0.6); // 60% word match
    }

    /**
     * Complete a quest
     */
    completeQuest(quest, player) {
        quest.status = 'completed';
        quest.completedAt = Date.now();

        // Move to completed quests
        this.gameState.worldState.questsCompleted.push(quest);
        this.gameState.worldState.questsActive = this.gameState.worldState.questsActive.filter(q => q.id !== quest.id);

        // Award rewards
        if (quest.rewards) {
            if (quest.rewards.experience) {
                this.characterManager.awardExperience(player, quest.rewards.experience);
            }

            if (quest.rewards.items) {
                quest.rewards.items.forEach(item => {
                    this.addItemToInventory(player, item);
                });
            }
        }

        this.gameState.gameStats.questsCompleted++;

        // Check for chapter progression
        if (quest.chapter) {
            this.gameState.chapter = Math.max(this.gameState.chapter, quest.chapter + 1);
        }

        // Activate next quest in chain
        if (quest.nextQuest) {
            this.activateNextQuest(quest.nextQuest);
        }

        // Check for game completion
        if (quest.isEndGame) {
            this.triggerGameCompletion(player);
        }

        this.emit('questCompleted', {
            quest: quest,
            player: player,
            isEndGame: quest.isEndGame
        });
    }

    /**
     * Activate the next quest in the chain
     */
    activateNextQuest(questId) {
        const questChain = this.gameState.worldState.questChain;
        const nextQuest = questChain.find(q => q.id === questId);

        if (nextQuest && nextQuest.status === 'locked') {
            nextQuest.status = 'active';
            this.gameState.worldState.questsActive.push(nextQuest);

            this.emit('questActivated', {
                quest: nextQuest
            });
        }
    }

    /**
     * Trigger game completion sequence
     */
    triggerGameCompletion(player) {
        this.gameState.gameCompleted = true;
        this.gameState.completionTime = Date.now();

        // Calculate final score
        const finalScore = this.calculateFinalScore(player);
        this.gameState.finalScore = finalScore;

        // Unlock special ending content
        this.unlockEndingContent(player);

        this.emit('gameCompleted', {
            player: player,
            finalScore: finalScore,
            completionTime: this.gameState.completionTime,
            playTime: this.gameState.gameStats.playTime
        });
    }

    /**
     * Calculate final score based on player achievements
     */
    calculateFinalScore(player) {
        let score = 0;

        // Base score from level and experience
        score += player.level * 1000;
        score += player.experience;

        // Bonus for quests completed
        score += this.gameState.gameStats.questsCompleted * 500;

        // Bonus for locations discovered
        score += this.gameState.worldState.discovered.length * 200;

        // Bonus for NPCs encountered
        score += this.gameState.gameStats.npcsEncountered * 100;

        // Time bonus (faster completion = higher score)
        const playTimeHours = this.gameState.gameStats.playTime / (1000 * 60 * 60);
        const timeBonus = Math.max(0, 10000 - (playTimeHours * 100));
        score += timeBonus;

        return Math.round(score);
    }

    /**
     * Unlock special ending content
     */
    unlockEndingContent(player) {
        // Add special ending items
        this.addItemToInventory(player, 'Certificate of Ascension');
        this.addItemToInventory(player, 'Eternal Flame');

        // Unlock all locations for exploration
        Object.keys(CONFIG.locations).forEach(location => {
            if (!this.gameState.worldState.discovered.includes(location)) {
                this.gameState.worldState.discovered.push(location);
            }
        });

        // Grant maximum stats
        Object.keys(player.stats).forEach(stat => {
            player.stats[stat] = Math.max(player.stats[stat], 25);
        });

        // Full health and vril restoration
        player.health = player.maxHealth;
        player.vril = player.maxVril;
    }

    /**
     * Check for random events
     */
    checkForRandomEvents() {
        if (Math.random() > 0.92) { // 8% chance
            setTimeout(() => {
                this.triggerRandomEvent();
            }, Math.random() * 10000 + 5000); // 5-15 seconds delay
        }
    }

    /**
     * Trigger a random environmental event
     */
    triggerRandomEvent() {
        const events = [
            "The crystalline walls pulse with renewed energy, casting dancing shadows.",
            "A distant harmonic tone echoes through the caverns, resonating in your bones.",
            "The air shimmers as reality briefly fluctuates around you.",
            "You sense an ancient presence observing from the ethereal realm.",
            "The vril currents suddenly intensify, making your skin tingle.",
            "Whispers in an unknown language drift on the underground breeze.",
            "A faint aurora of colored light plays across the cavern ceiling.",
            "The ground trembles slightly as massive machinery operates far below."
        ];
        
        const event = events[Math.floor(Math.random() * events.length)];
        
        this.addToMessageHistory('environment', 'The Inner Realm', event);
        
        this.emit('randomEvent', {
            event: event,
            timestamp: Date.now()
        });
    }

    /**
     * Add message to history
     */
    addToMessageHistory(type, author, content) {
        const message = {
            type,
            author,
            content,
            timestamp: Date.now(),
            location: this.gameState.location
        };
        
        this.gameState.messageHistory.push(message);
        
        // Limit history size
        if (this.gameState.messageHistory.length > CONFIG.game.maxMessages) {
            this.gameState.messageHistory.shift();
        }
    }

    /**
     * Get recent message history
     */
    getRecentHistory(count = 5) {
        return this.gameState.messageHistory.slice(-count);
    }

    /**
     * Get NPCs in current location
     */
    getNearbyNPCs() {
        return Array.from(this.gameState.npcs.values()).filter(npc => 
            npc.location === this.gameState.location
        );
    }

    /**
     * Get available items in current location
     */
    getAvailableItems() {
        const location = CONFIG.locations[this.gameState.location];
        return location ? location.items || [] : [];
    }

    /**
     * Update play time
     */
    updatePlayTime() {
        this.gameState.gameStats.playTime = Date.now() - this.gameState.gameStats.startTime;
    }

    /**
     * Save game state manually
     */
    async saveGame() {
        try {
            await this.saveManager.saveGame({
                gameState: this.gameState,
                timestamp: Date.now(),
                version: CONFIG.game.version
            });
            return true;
        } catch (error) {
            console.error('Manual save failed:', error);
            throw error;
        }
    }

    /**
     * Auto-save game state
     */
    async autoSave() {
        try {
            await this.saveManager.saveGame({
                gameState: this.gameState,
                timestamp: Date.now(),
                version: CONFIG.game.version
            });
        } catch (error) {
            console.warn('Auto-save failed:', error);
        }
    }

    /**
     * Event system methods
     */
    on(event, callback) {
        if (!this.eventListeners.has(event)) {
            this.eventListeners.set(event, []);
        }
        this.eventListeners.get(event).push(callback);
    }

    emit(event, data) {
        if (this.eventListeners.has(event)) {
            this.eventListeners.get(event).forEach(callback => {
                try {
                    callback(data);
                } catch (error) {
                    console.error(`Error in event listener for ${event}:`, error);
                }
            });
        }
    }

    /**
     * Get current game state
     */
    getGameState() {
        return { ...this.gameState };
    }

    /**
     * Award experience for player actions
     */
    awardActionExperience(player, action) {
        let experienceGained = CONFIG.mechanics.experience.basePerAction;

        // Bonus for creative/detailed actions
        if (action.length > 50) {
            experienceGained += CONFIG.mechanics.experience.bonusForCreativity;
        }

        // Bonus for roleplay elements
        if (action.toLowerCase().includes('as a ') || action.toLowerCase().includes('my character')) {
            experienceGained += CONFIG.mechanics.experience.bonusForRoleplay;
        }

        // Bonus for using class abilities
        const classConfig = CONFIG.characterClasses[player.class];
        if (classConfig && classConfig.abilities.some(ability =>
            action.toLowerCase().includes(ability.toLowerCase()))) {
            experienceGained += 15;
        }

        // Award the experience
        if (experienceGained > CONFIG.mechanics.experience.basePerAction) {
            this.characterManager.awardExperience(player, experienceGained);
        }
    }

    /**
     * Cleanup resources
     */
    destroy() {
        if (this.autoSaveTimer) {
            clearInterval(this.autoSaveTimer);
        }

        this.eventListeners.clear();
    }
}
