/**
 * AGARTHA RPG - CHARACTER MANAGER
 * Handles character creation, progression, and management
 */

import { CONFIG, getCharacterClass } from './config.js';
import { LORE, getCharacterLore } from './lore.js';

/**
 * Character Manager Class
 * Manages character creation, stats, progression, and abilities
 */
export class CharacterManager {
    constructor() {
        this.characters = new Map();
        this.activeCharacter = null;
        this.experienceTable = this.generateExperienceTable();
    }

    /**
     * Create a new character
     */
    createCharacter(name, characterClass, description = '') {
        const classConfig = getCharacterClass(characterClass);
        if (!classConfig) {
            throw new Error(`Unknown character class: ${characterClass}`);
        }

        // Generate base stats with some randomization
        const baseStats = { ...classConfig.baseStats };
        const stats = this.randomizeStats(baseStats);
        
        // Calculate derived stats
        const health = stats.vitality * CONFIG.mechanics.health.baseMultiplier;
        const vril = stats.vril * CONFIG.mechanics.vril.baseMultiplier;

        const character = {
            // Basic Info
            id: this.generateCharacterId(),
            name: name.trim(),
            class: characterClass,
            description: description.trim() || this.generateDefaultDescription(characterClass),
            
            // Core Stats
            stats: stats,
            level: 1,
            experience: 0,
            experienceToNext: this.getExperienceForLevel(2),
            
            // Health & Resources
            health: health,
            maxHealth: health,
            vril: vril,
            maxVril: vril,
            
            // Progression
            skillPoints: 0,
            attributePoints: 0,
            
            // Abilities & Skills
            abilities: [...classConfig.abilities],
            skills: this.initializeSkills(characterClass),
            
            // Equipment & Inventory
            equipment: this.initializeEquipment(classConfig),
            inventory: [...classConfig.startingItems],
            
            // Character State
            status: 'healthy',
            conditions: [],
            location: 'Crystal Gates of Shambhala',
            
            // Progression Tracking
            achievements: [],
            questsCompleted: 0,
            locationsVisited: 1,
            npcsEncountered: 0,
            
            // Timestamps
            created: Date.now(),
            lastPlayed: Date.now(),
            totalPlayTime: 0,
            
            // Lore & Background
            heritage: this.getCharacterHeritage(characterClass),
            personalityTraits: this.generatePersonalityTraits(characterClass),
            backstory: this.generateBackstory(characterClass, name)
        };

        this.characters.set(character.id, character);
        return character;
    }

    /**
     * Generate randomized stats based on class base stats
     */
    randomizeStats(baseStats) {
        const randomized = {};
        
        for (const [stat, baseValue] of Object.entries(baseStats)) {
            // Add random variation (-2 to +3)
            const variation = Math.floor(Math.random() * 6) - 2;
            randomized[stat] = Math.max(1, baseValue + variation);
        }
        
        return randomized;
    }

    /**
     * Initialize character skills
     */
    initializeSkills(characterClass) {
        const baseSkills = {
            // Combat Skills
            melee: 1,
            ranged: 1,
            defense: 1,
            
            // Mystical Skills
            vrilManipulation: 1,
            crystalResonance: 1,
            telepathy: 1,
            
            // Knowledge Skills
            ancientLore: 1,
            technology: 1,
            linguistics: 1,
            
            // Exploration Skills
            perception: 1,
            stealth: 1,
            survival: 1
        };

        // Boost skills based on character class
        const classBoosts = {
            'Crystal Keeper': { crystalResonance: 3, vrilManipulation: 2, ancientLore: 2 },
            'Vril Engineer': { vrilManipulation: 3, technology: 3, defense: 2 },
            'Lemurian Scholar': { ancientLore: 3, linguistics: 3, telepathy: 2 },
            'Atlantean Warrior': { melee: 3, defense: 3, ranged: 2 },
            'Inner Earth Scout': { perception: 3, stealth: 3, survival: 2 },
            'Light Weaver': { telepathy: 3, crystalResonance: 2, vrilManipulation: 2 }
        };

        const boosts = classBoosts[characterClass] || {};
        for (const [skill, boost] of Object.entries(boosts)) {
            if (baseSkills[skill]) {
                baseSkills[skill] += boost;
            }
        }

        return baseSkills;
    }

    /**
     * Initialize starting equipment
     */
    initializeEquipment(classConfig) {
        return {
            weapon: classConfig.startingItems.find(item => 
                item.includes('Blade') || item.includes('Staff') || item.includes('Focus')
            ) || null,
            armor: classConfig.startingItems.find(item => 
                item.includes('Armor') || item.includes('Robes')
            ) || null,
            accessory: classConfig.startingItems.find(item => 
                item.includes('Amplifier') || item.includes('Crystal') || item.includes('Seal')
            ) || null
        };
    }

    /**
     * Get character heritage based on class
     */
    getCharacterHeritage(characterClass) {
        const heritageMap = {
            'Crystal Keeper': 'agarthan',
            'Vril Engineer': 'agarthan',
            'Lemurian Scholar': 'lemurian',
            'Atlantean Warrior': 'atlantean',
            'Inner Earth Scout': 'agarthan',
            'Light Weaver': 'lemurian'
        };

        return heritageMap[characterClass] || 'agarthan';
    }

    /**
     * Generate personality traits based on class
     */
    generatePersonalityTraits(characterClass) {
        const traitSets = {
            'Crystal Keeper': ['Contemplative', 'Harmonious', 'Intuitive', 'Patient'],
            'Vril Engineer': ['Analytical', 'Innovative', 'Practical', 'Curious'],
            'Lemurian Scholar': ['Wise', 'Peaceful', 'Scholarly', 'Empathetic'],
            'Atlantean Warrior': ['Honorable', 'Disciplined', 'Protective', 'Proud'],
            'Inner Earth Scout': ['Adventurous', 'Observant', 'Independent', 'Resourceful'],
            'Light Weaver': ['Mystical', 'Creative', 'Insightful', 'Serene']
        };

        const traits = traitSets[characterClass] || ['Mysterious', 'Determined', 'Adaptable'];
        
        // Randomly select 2-3 traits
        const selectedTraits = [];
        const numTraits = Math.floor(Math.random() * 2) + 2; // 2-3 traits
        
        while (selectedTraits.length < numTraits && selectedTraits.length < traits.length) {
            const trait = traits[Math.floor(Math.random() * traits.length)];
            if (!selectedTraits.includes(trait)) {
                selectedTraits.push(trait);
            }
        }
        
        return selectedTraits;
    }

    /**
     * Generate character backstory
     */
    generateBackstory(characterClass, name) {
        const backstories = {
            'Crystal Keeper': `${name} was drawn to the resonant frequencies of the great crystals from an early age, spending countless hours in meditation within the crystal caverns.`,
            'Vril Engineer': `${name} showed exceptional aptitude for understanding the flow of vril energy, apprenticing under the master engineers who maintain Agartha's infrastructure.`,
            'Lemurian Scholar': `${name} is a keeper of the ancient memories, trained in the telepathic traditions passed down from the survivors of lost Lemuria.`,
            'Atlantean Warrior': `${name} carries the proud martial traditions of Atlantis, trained in combat techniques that blend physical prowess with crystal-enhanced abilities.`,
            'Inner Earth Scout': `${name} grew up exploring the vast tunnel networks, developing an intimate knowledge of the hidden paths that connect the underground realms.`,
            'Light Weaver': `${name} discovered the ability to manipulate light and energy patterns, studying the sacred geometries that underlie reality itself.`
        };

        return backstories[characterClass] || `${name} seeks to uncover the mysteries of the inner Earth and their own hidden potential.`;
    }

    /**
     * Generate default character description
     */
    generateDefaultDescription(characterClass) {
        const descriptions = {
            'Crystal Keeper': 'A serene figure with eyes that reflect the inner light of crystals, radiating calm wisdom.',
            'Vril Engineer': 'A practical individual with hands that seem to hum with energy, always ready to interface with ancient technology.',
            'Lemurian Scholar': 'An ethereal being with an elongated skull and large, knowing eyes that seem to hold ancient memories.',
            'Atlantean Warrior': 'A tall, imposing figure with the bearing of nobility and the scars of countless battles.',
            'Inner Earth Scout': 'A lean, agile explorer with keen eyes and the weathered look of one who has seen many hidden places.',
            'Light Weaver': 'A luminous being who seems to shimmer with inner light, their presence both calming and otherworldly.'
        };

        return descriptions[characterClass] || 'A mysterious traveler seeking ancient wisdom in the depths of Agartha.';
    }

    /**
     * Update character stats
     */
    updateCharacterStats(character, changes) {
        if (changes.health !== undefined) {
            character.health = Math.max(0, Math.min(character.maxHealth, character.health + changes.health));
        }
        
        if (changes.vril !== undefined) {
            character.vril = Math.max(0, Math.min(character.maxVril, character.vril + changes.vril));
        }
        
        if (changes.experience !== undefined) {
            this.awardExperience(character, changes.experience);
        }
        
        // Update status based on health
        if (character.health <= 0) {
            character.status = 'unconscious';
        } else if (character.health < character.maxHealth * 0.25) {
            character.status = 'critical';
        } else if (character.health < character.maxHealth * 0.5) {
            character.status = 'wounded';
        } else {
            character.status = 'healthy';
        }
        
        character.lastPlayed = Date.now();
    }

    /**
     * Award experience and handle level ups
     */
    awardExperience(character, amount) {
        character.experience += amount;
        
        // Check for level up
        while (character.experience >= this.getExperienceForLevel(character.level + 1)) {
            this.levelUp(character);
        }
        
        // Update experience to next level
        character.experienceToNext = this.getExperienceForLevel(character.level + 1) - character.experience;
    }

    /**
     * Handle character level up
     */
    levelUp(character) {
        character.level++;
        
        // Award stat increases
        const statIncrease = this.calculateStatIncrease(character);
        for (const [stat, increase] of Object.entries(statIncrease)) {
            character.stats[stat] += increase;
        }
        
        // Increase health and vril
        const healthIncrease = character.stats.vitality * 2;
        const vrilIncrease = character.stats.vril * 1;
        
        character.maxHealth += healthIncrease;
        character.health += healthIncrease; // Full heal on level up
        character.maxVril += vrilIncrease;
        character.vril += vrilIncrease; // Full restore on level up
        
        // Award skill points and attribute points
        character.skillPoints += 3;
        character.attributePoints += 1;
        
        // Learn new abilities at certain levels
        this.checkForNewAbilities(character);
        
        console.log(`🎉 ${character.name} reached level ${character.level}!`);
    }

    /**
     * Calculate stat increases on level up
     */
    calculateStatIncrease(character) {
        const classConfig = getCharacterClass(character.class);
        const primaryStat = classConfig.primaryStat;
        
        const increases = {};
        
        // Primary stat gets guaranteed increase
        increases[primaryStat] = 1;
        
        // Random chance for other stats
        for (const stat of Object.keys(character.stats)) {
            if (stat !== primaryStat && Math.random() > 0.7) {
                increases[stat] = (increases[stat] || 0) + 1;
            }
        }
        
        return increases;
    }

    /**
     * Check for new abilities at level milestones
     */
    checkForNewAbilities(character) {
        const abilityMilestones = {
            3: this.getClassAbility(character.class, 'novice'),
            5: this.getClassAbility(character.class, 'apprentice'),
            7: this.getClassAbility(character.class, 'journeyman'),
            10: this.getClassAbility(character.class, 'expert'),
            15: this.getClassAbility(character.class, 'master'),
            20: this.getClassAbility(character.class, 'grandmaster')
        };
        
        const newAbility = abilityMilestones[character.level];
        if (newAbility && !character.abilities.includes(newAbility)) {
            character.abilities.push(newAbility);
            console.log(`✨ ${character.name} learned new ability: ${newAbility}`);
        }
    }

    /**
     * Get class-specific abilities by tier
     */
    getClassAbility(characterClass, tier) {
        const abilities = {
            'Crystal Keeper': {
                novice: 'Crystal Sight',
                apprentice: 'Energy Shield',
                journeyman: 'Crystal Network',
                expert: 'Harmonic Resonance',
                master: 'Crystal Mastery',
                grandmaster: 'Unity with the Matrix'
            },
            'Vril Engineer': {
                novice: 'Energy Scan',
                apprentice: 'Tech Interface',
                journeyman: 'Vril Overcharge',
                expert: 'System Mastery',
                master: 'Energy Architect',
                grandmaster: 'Vril Singularity'
            },
            'Lemurian Scholar': {
                novice: 'Ancient Lore',
                apprentice: 'Telepathic Link',
                journeyman: 'Memory Access',
                expert: 'Akashic Reading',
                master: 'Consciousness Bridge',
                grandmaster: 'Universal Knowledge'
            },
            'Atlantean Warrior': {
                novice: 'Combat Focus',
                apprentice: 'Crystal Weapon',
                journeyman: 'Battle Meditation',
                expert: 'Warrior\'s Fury',
                master: 'Legendary Technique',
                grandmaster: 'Avatar of War'
            },
            'Inner Earth Scout': {
                novice: 'Tunnel Vision',
                apprentice: 'Silent Step',
                journeyman: 'Path Finding',
                expert: 'Shadow Merge',
                master: 'Realm Walker',
                grandmaster: 'Master Explorer'
            },
            'Light Weaver': {
                novice: 'Light Touch',
                apprentice: 'Geometric Sight',
                journeyman: 'Pattern Weaving',
                expert: 'Reality Shaping',
                master: 'Light Mastery',
                grandmaster: 'Creator of Worlds'
            }
        };
        
        return abilities[characterClass]?.[tier] || null;
    }

    /**
     * Generate experience table
     */
    generateExperienceTable() {
        const table = [0]; // Level 1 starts at 0 XP

        for (let level = 2; level <= 50; level++) {
            // Balanced progression for long gameplay
            let baseXP;
            if (level <= 10) {
                // Early levels: 100, 250, 450, 700, 1000, 1350, 1750, 2200, 2700, 3250
                baseXP = Math.floor(100 * level * (level - 1) / 2);
            } else if (level <= 25) {
                // Mid levels: slower progression
                baseXP = 3250 + Math.floor(500 * (level - 10) * 1.2);
            } else {
                // High levels: much slower progression
                baseXP = 12250 + Math.floor(1000 * (level - 25) * 1.5);
            }
            table.push(baseXP);
        }

        return table;
    }

    /**
     * Get experience required for a specific level
     */
    getExperienceForLevel(level) {
        if (level <= 1) return 0;
        if (level > this.experienceTable.length) {
            // Extrapolate for very high levels
            return Math.floor(100 * Math.pow(level - 1, 1.5));
        }
        return this.experienceTable[level - 1];
    }

    /**
     * Get character lore information
     */
    getCharacterLore(characterClass) {
        return getCharacterLore(characterClass);
    }

    /**
     * Generate unique character ID
     */
    generateCharacterId() {
        return 'char_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    /**
     * Get character by ID
     */
    getCharacter(id) {
        return this.characters.get(id);
    }

    /**
     * Set active character
     */
    setActiveCharacter(character) {
        this.activeCharacter = character;
    }

    /**
     * Get active character
     */
    getActiveCharacter() {
        return this.activeCharacter;
    }

    /**
     * Get all characters
     */
    getAllCharacters() {
        return Array.from(this.characters.values());
    }

    /**
     * Delete character
     */
    deleteCharacter(id) {
        return this.characters.delete(id);
    }

    /**
     * Export character data
     */
    exportCharacter(character) {
        return JSON.stringify(character, null, 2);
    }

    /**
     * Import character data
     */
    importCharacter(characterData) {
        try {
            const character = JSON.parse(characterData);
            this.characters.set(character.id, character);
            return character;
        } catch (error) {
            throw new Error('Invalid character data');
        }
    }
}
