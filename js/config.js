/**
 * AGARTHA RPG - GAME CONFIGURATION
 * Central configuration file for all game settings
 */

export const CONFIG = {
    // ===== GAME SETTINGS =====
    game: {
        title: "Agartha: The Lost City of Light",
        version: "2.0.0",
        maxPlayers: 12,
        maxMessages: 150,
        saveInterval: 45000, // 45 seconds
        autoSaveEnabled: true,
        debugMode: false
    },

    // ===== AI MODEL CONFIGURATIONS =====
    models: {
        'Phi-3-mini-4k-instruct-q4f16_1-MLC': {
            name: 'Phi-3 Mini',
            displayName: '⭐ Phi-3 Mini (Recommended)',
            description: 'Microsoft\'s efficient small language model with excellent RPG capabilities',
            contextLength: 4096,
            temperature: 0.8,
            topP: 0.95,
            maxTokens: 250,
            size: '2.2GB',
            params: '3.8B',
            speed: 'Fast',
            recommended: true
        },
        'Llama-3.1-8B-Instruct-q4f16_1-MLC': {
            name: '<PERSON>lama 3.1 8B',
            displayName: '🦙 Llama 3.1 8B',
            description: 'Meta\'s powerful open model with strong narrative abilities',
            contextLength: 8192,
            temperature: 0.9,
            topP: 0.95,
            maxTokens: 300,
            size: '4.6GB',
            params: '8B',
            speed: 'Powerful'
        },
        'gemma-2-2b-it-q4f16_1-MLC': {
            name: 'Gemma 2 2B',
            displayName: '💎 Gemma 2 2B',
            description: 'Google\'s efficient model optimized for creative tasks',
            contextLength: 8192,
            temperature: 0.85,
            topP: 0.9,
            maxTokens: 280,
            size: '1.6GB',
            params: '2B',
            speed: 'Creative'
        },
        'TinyLlama-1.1B-Chat-v1.0-q4f16_1-MLC': {
            name: 'TinyLlama 1.1B',
            displayName: '🚀 TinyLlama 1.1B',
            description: 'Lightweight model for devices with limited resources',
            contextLength: 2048,
            temperature: 0.7,
            topP: 0.9,
            maxTokens: 200,
            size: '637MB',
            params: '1.1B',
            speed: 'Ultra-fast'
        }
    },

    // ===== CHARACTER CLASSES =====
    characterClasses: {
        'Crystal Keeper': {
            name: 'Crystal Keeper',
            title: 'Master of Ancient Energies',
            description: 'Harnesses the power of crystalline matrices and sacred geometries',
            primaryStat: 'vril',
            baseStats: { vril: 18, wisdom: 16, resonance: 15, vitality: 12, agility: 10, strength: 9 },
            startingItems: ['Crystal Focus', 'Energy Amplifier', 'Meditation Beads'],
            abilities: ['Crystal Resonance', 'Energy Channeling', 'Harmonic Healing'],
            lore: 'Descendants of the first Agarthans who learned to commune with the living crystals that power the inner realm.'
        },
        'Vril Engineer': {
            name: 'Vril Engineer',
            title: 'Manipulator of Life Force',
            description: 'Masters the fundamental life energy that flows through all things',
            primaryStat: 'resonance',
            baseStats: { vril: 17, wisdom: 14, resonance: 16, vitality: 14, agility: 12, strength: 11 },
            startingItems: ['Vril Conductor', 'Energy Scanner', 'Repair Kit'],
            abilities: ['Vril Manipulation', 'Technology Interface', 'Energy Shield'],
            lore: 'Engineers who maintain the great machines of Agartha, powered by the mysterious vril energy.'
        },
        'Lemurian Scholar': {
            name: 'Lemurian Scholar',
            title: 'Keeper of Lost Knowledge',
            description: 'Preserves the ancient wisdom of the sunken continent',
            primaryStat: 'wisdom',
            baseStats: { vril: 15, wisdom: 18, resonance: 14, vitality: 13, agility: 11, strength: 8 },
            startingItems: ['Ancient Tome', 'Translation Crystal', 'Memory Stone'],
            abilities: ['Ancient Knowledge', 'Language Mastery', 'Psychic Reading'],
            lore: 'Survivors of Lemuria who escaped the great cataclysm and now guard the accumulated wisdom of ages.'
        },
        'Atlantean Warrior': {
            name: 'Atlantean Warrior',
            title: 'Guardian of the Old Ways',
            description: 'Elite fighters trained in the martial traditions of lost Atlantis',
            primaryStat: 'vitality',
            baseStats: { vril: 13, wisdom: 12, resonance: 14, vitality: 18, agility: 15, strength: 16 },
            startingItems: ['Atlantean Blade', 'Crystal Armor', 'Warrior\'s Seal'],
            abilities: ['Combat Mastery', 'Tactical Awareness', 'Berserker Rage'],
            lore: 'Elite warriors who fled Atlantis before its destruction, bringing their martial knowledge to Agartha.'
        },
        'Inner Earth Scout': {
            name: 'Inner Earth Scout',
            title: 'Explorer of Hidden Realms',
            description: 'Expert navigators of the vast tunnel networks beneath the surface',
            primaryStat: 'agility',
            baseStats: { vril: 14, wisdom: 15, resonance: 16, vitality: 16, agility: 17, strength: 12 },
            startingItems: ['Pathfinder\'s Compass', 'Climbing Gear', 'Emergency Rations'],
            abilities: ['Tunnel Sense', 'Stealth Movement', 'Survival Instinct'],
            lore: 'Native-born Agarthans who know every passage and secret route through the endless underground realm.'
        },
        'Light Weaver': {
            name: 'Light Weaver',
            title: 'Channeler of Sacred Geometries',
            description: 'Manipulates the fundamental patterns of light and energy',
            primaryStat: 'resonance',
            baseStats: { vril: 16, wisdom: 17, resonance: 18, vitality: 11, agility: 13, strength: 9 },
            startingItems: ['Prism Focus', 'Light Crystals', 'Geometric Tools'],
            abilities: ['Light Manipulation', 'Illusion Weaving', 'Sacred Geometry'],
            lore: 'Mystics who understand that reality itself is woven from patterns of light and consciousness.'
        }
    },

    // ===== WORLD LOCATIONS =====
    locations: {
        'Crystal Gates of Shambhala': {
            name: 'Crystal Gates of Shambhala',
            description: 'Massive amethyst pillars that serve as the entrance to the inner realm',
            type: 'gateway',
            connections: ['Shambhala Central Plaza', 'Outer Tunnels'],
            npcs: ['Gate Guardian Zephyr'],
            items: ['Welcome Crystal'],
            atmosphere: 'mystical'
        },
        'Shambhala Central Plaza': {
            name: 'Shambhala Central Plaza',
            description: 'The heart of the great city, where beings from all realms gather',
            type: 'city',
            connections: ['Crystal Gates of Shambhala', 'Hall of Records', 'Vril Power Station', 'Market of Wonders'],
            npcs: ['Elder Thoth', 'Merchant Kira', 'Herald Aeon'],
            items: ['City Map', 'Communication Crystal'],
            atmosphere: 'bustling'
        },
        'Hall of Records': {
            name: 'Hall of Records',
            description: 'Ancient library containing the complete history of Earth and beyond',
            type: 'library',
            connections: ['Shambhala Central Plaza', 'Akashic Chambers'],
            npcs: ['Librarian Sophia', 'Scribe Hermes'],
            items: ['Historical Scrolls', 'Memory Crystals'],
            atmosphere: 'scholarly'
        },
        'Telos Beneath Mt. Shasta': {
            name: 'Telos Beneath Mt. Shasta',
            description: 'Hidden city of the Telosians, masters of telepathic communication',
            type: 'city',
            connections: ['Surface Tunnels', 'Telepathic Relay Station'],
            npcs: ['High Priestess Adama', 'Telepathic Guide Mikos'],
            items: ['Telepathic Amplifier', 'Telosian Robes'],
            atmosphere: 'serene'
        },
        'Vril Power Station': {
            name: 'Vril Power Station',
            description: 'Massive energy distribution center powered by crystalline matrices',
            type: 'facility',
            connections: ['Shambhala Central Plaza', 'Deep Energy Conduits'],
            npcs: ['Vril Engineer Kael', 'Energy Technician Zara'],
            items: ['Vril Conductor', 'Energy Scanner'],
            atmosphere: 'energetic'
        },
        'Akashic Chambers': {
            name: 'Akashic Chambers',
            description: 'Sacred repository of all knowledge and memory in crystalline form',
            type: 'sanctuary',
            connections: ['Hall of Records', 'Consciousness Nexus'],
            npcs: ['Akashic Keeper', 'Memory Guardian'],
            items: ['Memory Crystal', 'Knowledge Codex'],
            atmosphere: 'transcendent'
        },
        'Central Sun Chamber': {
            name: 'Central Sun Chamber',
            description: 'The heart of Agartha, where the Smoky God illuminates the inner realm',
            type: 'sacred_site',
            connections: ['Deep Tunnels', 'Energy Nexus'],
            npcs: ['Solar Guardian', 'Flame Keeper'],
            items: ['Solar Crystal', 'Light Essence'],
            atmosphere: 'divine'
        }
    },

    // ===== GAME MECHANICS =====
    mechanics: {
        dice: {
            criticalFailure: [1, 2],
            failure: [3, 5],
            partialSuccess: [6, 10],
            success: [11, 15],
            criticalSuccess: [16, 20]
        },
        experience: {
            basePerAction: 10,
            bonusForCreativity: 25,
            bonusForRoleplay: 15,
            levelUpThreshold: 1000
        },
        health: {
            baseMultiplier: 10,
            regenRate: 0.1, // per minute
            criticalThreshold: 0.25
        },
        vril: {
            baseMultiplier: 5,
            regenRate: 0.2, // per minute
            overchargeThreshold: 1.5
        }
    },

    // ===== UI SETTINGS =====
    ui: {
        typingSpeed: 30, // milliseconds per character
        messageDisplayTime: 5000,
        animationDuration: 300,
        maxChatHistory: 100,
        autoScrollEnabled: true,
        soundEnabled: true,
        particleEffects: true
    },

    // ===== AUDIO SETTINGS =====
    audio: {
        masterVolume: 0.7,
        musicVolume: 0.5,
        sfxVolume: 0.8,
        ambientVolume: 0.4,
        voiceVolume: 0.9
    },

    // ===== NETWORK SETTINGS =====
    network: {
        maxRetries: 3,
        retryDelay: 1000,
        timeout: 30000,
        heartbeatInterval: 60000
    },

    // ===== STORAGE SETTINGS =====
    storage: {
        localStorageKey: 'agarthaGameSave',
        compressionEnabled: true,
        encryptionEnabled: false,
        maxSaveSlots: 5
    }
};

// ===== VALIDATION FUNCTIONS =====

export function validateConfig() {
    const errors = [];
    
    // Validate model configurations
    for (const [modelId, config] of Object.entries(CONFIG.models)) {
        if (!config.name || !config.contextLength || !config.temperature) {
            errors.push(`Invalid model configuration for ${modelId}`);
        }
    }
    
    // Validate character classes
    for (const [className, config] of Object.entries(CONFIG.characterClasses)) {
        if (!config.baseStats || !config.abilities || !config.startingItems) {
            errors.push(`Invalid character class configuration for ${className}`);
        }
    }
    
    if (errors.length > 0) {
        console.error('Configuration validation errors:', errors);
        return false;
    }
    
    return true;
}

// ===== UTILITY FUNCTIONS =====

export function getModelConfig(modelId) {
    return CONFIG.models[modelId] || null;
}

export function getCharacterClass(className) {
    return CONFIG.characterClasses[className] || null;
}

export function getLocation(locationName) {
    return CONFIG.locations[locationName] || null;
}

export function getDiceResult(roll) {
    const { dice } = CONFIG.mechanics;
    
    if (dice.criticalFailure.includes(roll)) return 'critical_failure';
    if (dice.failure.some(range => roll >= range[0] && roll <= range[1])) return 'failure';
    if (dice.partialSuccess.some(range => roll >= range[0] && roll <= range[1])) return 'partial_success';
    if (dice.success.some(range => roll >= range[0] && roll <= range[1])) return 'success';
    if (dice.criticalSuccess.includes(roll)) return 'critical_success';
    
    return 'unknown';
}

// Initialize and validate configuration on load
if (typeof window !== 'undefined') {
    window.addEventListener('load', () => {
        if (!validateConfig()) {
            console.warn('Configuration validation failed. Some features may not work correctly.');
        }
    });
}
